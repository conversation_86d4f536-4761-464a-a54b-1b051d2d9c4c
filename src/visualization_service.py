"""
可视化服务模块

为 Google Maps Grid Search 项目提供统一的、解耦的实时可视化服务。
此模块是可视化的单一事实来源，负责生成所有HTML地图报告。
"""

import os
import time
from pathlib import Path
from typing import Optional, Dict, Any, List, Tuple, Callable, Set
from dataclasses import dataclass, field
import logging
import threading
import itertools
import math

from src.data_models import SearchOrchestration, GridCell, SearchStatus

logger = logging.getLogger(__name__)

try:
    import folium
    FOLIUM_AVAILABLE = True
except ImportError:
    FOLIUM_AVAILABLE = False
    logger.warning("Folium 库不可用，将跳过可视化服务")

# --- NEW: Centralized Style Configuration ---

# A vibrant, colorblind-friendly color palette for layers
LAYER_COLORS = [
    "#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd",
    "#8c564b", "#e377c2", "#7f7f7f", "#bcbd22", "#17becf"
]

# Styles for different cell statuses, focusing on line style rather than color
STATUS_STYLES = {
    SearchStatus.PENDING: {"weight": 1, "dash_array": "5,5", "opacity": 0.6},
    SearchStatus.PROCESSING: {"weight": 2, "dash_array": "10,5", "opacity": 0.8},
    SearchStatus.SEARCH_COMPLETE: {"weight": 2, "dash_array": None, "opacity": 0.9},
    SearchStatus.REFINEMENT_NEEDED: {"weight": 4, "dash_array": None, "opacity": 1.0},
    SearchStatus.REFINEMENT_COMPLETE: {"weight": 2, "dash_array": None, "opacity": 0.9},
    SearchStatus.FAILED: {"weight": 2, "dash_array": "1,3", "opacity": 0.8},
    SearchStatus.SKIPPED: {"weight": 1, "dash_array": "2,2", "opacity": 0.4},  # 移除 color，使用传入的 color
}
DEFAULT_STATUS_STYLE = {"weight": 1, "dash_array": None, "opacity": 0.7}
HIGHLIGHT_STYLE = {"color": "gold", "fillColor": "gold", "fillOpacity": 0.9, "radius": 8, "weight": 3}


@dataclass
class VisualizationState:
    """可视化状态内部类"""
    last_update: float = 0.0
    update_count: int = 0
    enabled: bool = True
    error_count: int = 0
    # 新增：固定边界缓存，防止地图偏移
    fixed_bounds: Optional[Tuple[float, float, float, float]] = None
    initial_bounds_set: bool = False


class VisualizationService:
    """
    解耦的可视化服务
    提供清晰、具说明性的可视化，帮助理解分层网格搜索的工作原理。
    """

    def __init__(self, work_dir: str, enabled: bool = True, update_interval: float = 1.0):
        """
        初始化可视化服务
        Args:
            work_dir: 工作目录路径
            enabled: 是否启用可视化
            update_interval: 实时更新的最小时间间隔（秒）
        """
        self.config = {
            "enabled": enabled and FOLIUM_AVAILABLE,
            "update_interval": update_interval,
            "output_filename": "visualization_map.html",
        }
        self.work_dir = self._validate_work_dir(work_dir)
        self.state = VisualizationState()
        self._lock = threading.Lock()  # 引入线程锁
        if not self.config["enabled"]:
            logger.info("可视化服务已禁用 (Folium不可用或配置禁用)")
        else:
            logger.info(f"可视化服务已启用，更新间隔: {update_interval}秒")

    def _validate_work_dir(self, work_dir: str) -> Path:
        """验证工作目录安全性和合法性"""
        try:
            path = Path(work_dir).resolve()
            path.mkdir(parents=True, exist_ok=True)
            if not os.access(path, os.W_OK):
                raise PermissionError(f"工作目录 {path} 不可写")
            return path
        except Exception as e:
            logger.error(f"工作目录验证失败: {e}")
            raise ValueError(f"无效的工作目录: {work_dir}")

    def _should_update(self, force: bool = False) -> bool:
        """检查是否应该更新可视化"""
        if not self.config["enabled"]:
            return False
        if force:
            return True
        # 减少更新间隔，提高实时性
        return time.time() - self.state.last_update >= self.config["update_interval"]

    def create_visualization(
        self,
        orchestration: SearchOrchestration,
        highlight_cell_id: Optional[str] = None,
        force: bool = False,
    ) -> bool:
        """
        创建或更新可视化地图。这是该服务唯一的公共绘图方法。
        """
        with self._lock:
            if not self._should_update(force):
                logger.debug(f"跳过可视化更新 (force={force}, enabled={self.config['enabled']})")
                return False

            try:
                if not orchestration or not orchestration.cells:
                    logger.debug("没有网格单元数据，跳过可视化")
                    return False

                output_file = self.work_dir / self.config["output_filename"]
                
                # 确保输出目录存在
                output_file.parent.mkdir(parents=True, exist_ok=True)
                
                # 计算地图视图边界（防止偏移）
                view_bounds = self._calculate_bounds(orchestration)
                map_obj = self._create_map_object(view_bounds)

                # --- NEW: Layer-based drawing ---
                layer_groups, used_colors = self._add_map_layers(map_obj, orchestration)

                if highlight_cell_id and highlight_cell_id in orchestration.cells:
                    self._add_highlight_marker(map_obj, orchestration.cells[highlight_cell_id])

                # 计算原始搜索区域边界（用于绘制边界矩形）
                search_bounds = self._calculate_original_search_bounds(orchestration)
                self._add_boundary_rectangle(map_obj, search_bounds)
                
                # --- NEW: Updated Legend and Info ---
                self._add_layer_legend(map_obj, used_colors, orchestration)
                self._add_status_legend(map_obj)
                self._add_info_panel(map_obj, orchestration)

                # --- NEW: Add Layer Control ---
                if layer_groups:
                    folium.LayerControl(collapsed=False).add_to(map_obj)

                # 使用临时文件避免并发写入问题
                temp_file = output_file.with_suffix('.tmp')
                try:
                    map_obj.save(str(temp_file))
                    # 原子性地重命名文件
                    temp_file.replace(output_file)
                    self._record_update()
                    logger.info(f"可视化已保存: {output_file}")
                    return True
                except Exception as e:
                    logger.error(f"保存可视化文件失败: {e}")
                    # 清理临时文件
                    if temp_file.exists():
                        temp_file.unlink()
                    return False

            except Exception as e:
                logger.error(f"可视化生成失败: {e}", exc_info=True)
                self.state.error_count += 1
                return False

    def _create_map_object(self, bounds: Tuple[float, float, float, float]) -> "folium.Map":
        """创建并配置folium地图对象"""
        center_lat = (bounds[0] + bounds[2]) / 2
        center_lng = (bounds[1] + bounds[3]) / 2
        map_obj = folium.Map(
            location=[center_lat, center_lng],
            zoom_start=13,
            tiles="CartoDB positron",  # Use a cleaner, less distracting base map
        )
        map_obj.fit_bounds([[bounds[0], bounds[1]], [bounds[2], bounds[3]]])
        return map_obj

    def _get_layer_color(self, layer_id: int) -> str:
        """Get a consistent color for a layer ID."""
        return LAYER_COLORS[layer_id % len(LAYER_COLORS)]

    def _add_map_layers(self, map_obj: "folium.Map", orchestration: SearchOrchestration) -> Tuple[Dict[int, Any], Dict[int, str]]:
        """
        将所有网格单元按层级绘制到不同的特性组中，并返回这些组。
        这允许用户在地图上独立地切换每个层级的可见性。
        """
        layer_groups: Dict[int, folium.FeatureGroup] = {}
        used_colors: Dict[int, str] = {}
        
        # Sort cells by layer to ensure correct drawing order if needed
        cells_by_layer = sorted(orchestration.cells.values(), key=lambda c: c.layer_id)
        
        all_layer_ids: Set[int] = {cell.layer_id for cell in cells_by_layer}

        for layer_id in sorted(list(all_layer_ids)):
            color = self._get_layer_color(layer_id)
            used_colors[layer_id] = color
            
            # Create a feature group for this layer
            group = folium.FeatureGroup(name=f"Layer {layer_id}", show=True)
            layer_groups[layer_id] = group
            
            # Add cells of the current layer to the group
            for cell in [c for c in cells_by_layer if c.layer_id == layer_id]:
                self._draw_cell(group, cell, color)
        
        # Add all feature groups to the map
        for group in layer_groups.values():
            group.add_to(map_obj)
            
        return layer_groups, used_colors

    def _draw_cell(self, target_layer: "folium.FeatureGroup", cell: GridCell, color: str):
        """
        绘制单个网格单元，显示搜索范围圆圈和层级半径信息。
        颜色由层级决定，线条样式由状态决定。
        """
        style = STATUS_STYLES.get(cell.status, DEFAULT_STATUS_STYLE).copy()  # 复制样式避免修改原字典
        
        # 对于跳过的网格，使用灰色
        if cell.status == SearchStatus.SKIPPED:
            style["color"] = "gray"
        else:
            style["color"] = color

        # 计算层级半径信息
        layer_radius_info = self._get_layer_radius_info(cell)
        
        # Draw the search radius circle
        folium.Circle(
            location=[cell.center_lat, cell.center_lng],
            radius=cell.search_radius,
            fill=False,
            popup=self._create_cell_popup(cell),
            **style,
        ).add_to(target_layer)

    def _create_cell_popup(self, cell: GridCell) -> str:
        """创建网格单元的弹出信息"""
        status_text = {
            SearchStatus.PENDING: "待处理",
            SearchStatus.PROCESSING: "处理中",
            SearchStatus.SEARCH_COMPLETE: "搜索完成",
            SearchStatus.REFINEMENT_NEEDED: "需要细化", 
            SearchStatus.REFINEMENT_COMPLETE: "细化完成",
            SearchStatus.FAILED: "处理失败",
            SearchStatus.SKIPPED: "跳过处理",
        }
        
        popup_html = f"""
        <div style="font-family: Arial, sans-serif; min-width: 200px;">
            <h4>网格单元: {cell.cell_id}</h4>
            <p><strong>状态:</strong> {status_text.get(cell.status, '未知')}</p>
            <p><strong>层级:</strong> {cell.layer_id}</p>
            <p><strong>中心:</strong> ({cell.center_lat:.6f}, {cell.center_lng:.6f})</p>
            <p><strong>半径:</strong> {cell.search_radius:.0f}m</p>
            <p><strong>结果数:</strong> {cell.results_count}</p>
            <p><strong>API调用:</strong> {cell.api_calls_count}</p>
        """
        
        if cell.status == SearchStatus.SKIPPED:
            skipped_reason = cell.metadata.get("skipped_reason", "未知原因")
            grandparent_id = cell.metadata.get("grandparent_cell_id", "未知")
            popup_html += f"""
            <p><strong>跳过原因:</strong> {skipped_reason}</p>
            <p><strong>祖父网格:</strong> {grandparent_id}</p>
            """
        
        if cell.error_message:
            popup_html += f"<p><strong>错误:</strong> {cell.error_message}</p>"
        
        popup_html += "</div>"
        return popup_html

    def _get_layer_radius_info(self, cell: GridCell) -> str:
        """获取层级半径信息"""
        if cell.layer_id == 0:
            return "Initial Layer"
        else:
            # 计算层级半径变化
            parent_radius = cell.search_radius * 3.0  # 假设父层级半径是当前半径的3倍
            return f"Refined from Layer {cell.layer_id-1}<br>Parent Radius: ~{int(parent_radius)}m"

    def _add_highlight_marker(self, map_obj: "folium.Map", cell: GridCell):
        """添加高亮标记"""
        folium.CircleMarker(
            location=[cell.center_lat, cell.center_lng],
            popup=f"最近更新: {cell.cell_id}<br>Status: {cell.status.value}",
            **HIGHLIGHT_STYLE,
        ).add_to(map_obj)

    def _calculate_bounds(self, orchestration: SearchOrchestration) -> Tuple[float, float, float, float]:
        """计算合适的地图边界 - 修复版本，防止网格偏移"""
        if not orchestration.cells:
            return (52.51, 13.39, 52.53, 13.42)  # Default to Berlin center

        # 如果已经设置了固定边界，直接使用（防止偏移）
        if self.state.fixed_bounds is not None:
            logger.debug("使用固定边界，防止地图偏移")
            return self.state.fixed_bounds

        # 首次计算边界时，使用所有网格点建立固定边界，但只计算一次
        if not self.state.initial_bounds_set:
            # 使用所有网格点计算边界（包括搜索半径）
            lats = [c.center_lat for c in orchestration.cells.values()]
            lngs = [c.center_lng for c in orchestration.cells.values()]

            # 计算最大搜索半径，用于确定合适的padding
            max_radius = max(c.search_radius for c in orchestration.cells.values()) if orchestration.cells else 1000
            # 将半径转换为度数作为padding（使用平均纬度）
            avg_lat = sum(lats) / len(lats)
            lat_padding = max_radius / 111320  # 纬度padding
            lng_padding = max_radius / (111320 * math.cos(math.radians(avg_lat)))  # 经度padding

            # 添加额外的边距以确保所有细化网格都在视图内
            extra_padding_factor = 1.2  # 额外20%的边距
            lat_padding *= extra_padding_factor
            lng_padding *= extra_padding_factor

            fixed_bounds = (
                min(lats) - lat_padding,
                min(lngs) - lng_padding,
                max(lats) + lat_padding,
                max(lngs) + lng_padding
            )

            # 缓存固定边界
            self.state.fixed_bounds = fixed_bounds
            self.state.initial_bounds_set = True

            logger.info(f"建立固定地图边界: {fixed_bounds}")
            logger.info(f"基于 {len(orchestration.cells)} 个网格，最大半径: {max_radius}m")

            return fixed_bounds

        # 如果没有设置固定边界，使用动态计算（向后兼容）
        lats = [c.center_lat for c in orchestration.cells.values()]
        lngs = [c.center_lng for c in orchestration.cells.values()]
        padding = 0.005
        return (min(lats) - padding, min(lngs) - padding, max(lats) + padding, max(lngs) + padding)

    def _calculate_original_search_bounds(self, orchestration: SearchOrchestration) -> Tuple[float, float, float, float]:
        """计算原始搜索区域边界（用于绘制边界矩形）"""
        if not orchestration.cells:
            return (52.51, 13.39, 52.53, 13.42)  # Default to Berlin center

        # 基于L0层级网格计算原始搜索区域边界
        l0_cells = [c for c in orchestration.cells.values() if c.layer_id == 0]

        if l0_cells:
            # 使用L0网格点计算原始边界（不包含搜索半径扩展）
            lats = [c.center_lat for c in l0_cells]
            lngs = [c.center_lng for c in l0_cells]

            # 使用较小的padding，仅用于边界矩形显示
            padding = 0.002  # 更小的padding
            return (min(lats) - padding, min(lngs) - padding, max(lats) + padding, max(lngs) + padding)

        # 如果没有L0网格，使用所有网格
        lats = [c.center_lat for c in orchestration.cells.values()]
        lngs = [c.center_lng for c in orchestration.cells.values()]
        padding = 0.002
        return (min(lats) - padding, min(lngs) - padding, max(lats) + padding, max(lngs) + padding)

    def _add_boundary_rectangle(self, map_obj: "folium.Map", bounds: Tuple[float, float, float, float]):
        """添加搜索边界矩形"""
        folium.Rectangle(
            bounds=[[bounds[0], bounds[1]], [bounds[2], bounds[3]]],
            color="black",
            fill=False,
            weight=2,
            dash_array="10, 10",
            popup="Search Area Boundary",
        ).add_to(map_obj)

    def _add_layer_legend(self, map_obj: "folium.Map", used_colors: Dict[int, str], orchestration: SearchOrchestration):
        """为图层颜色添加图例，包含半径信息"""
        # 获取每个层级的半径信息
        layer_radii = {}
        for cell in orchestration.cells.values():
            if cell.layer_id not in layer_radii:
                layer_radii[cell.layer_id] = cell.search_radius
        
        legend_html = '''
        <div style="position: fixed; 
                    bottom: 20px; right: 10px; width: 180px;
                    background-color: rgba(255, 255, 255, 0.8); border:2px solid grey; z-index:9998;
                    font-size:12px; padding: 10px; border-radius: 5px;">
        <b>Layer Colors & Radii</b><br>
        '''
        for layer_id, color in sorted(used_colors.items()):
            radius = layer_radii.get(layer_id, "N/A")
            legend_html += f'<i style="background:{color}; border: 1px solid #555; width: 12px; height: 12px; display: inline-block; vertical-align: middle;"></i> Layer {layer_id} ({radius}m)<br>'
        legend_html += "</div>"
        map_obj.get_root().html.add_child(folium.Element(legend_html))

    def _add_status_legend(self, map_obj: "folium.Map"):
        """添加状态图例"""
        legend_html = """
        <div style="position: fixed; 
                    top: 10px; right: 10px; width: 200px; height: auto; 
                    background-color: white; border:2px solid grey; z-index:9999; 
                    font-size:14px; padding: 10px">
        <p><b>网格状态图例</b></p>
        """
        
        status_descriptions = {
            SearchStatus.PENDING: "待处理",
            SearchStatus.PROCESSING: "处理中", 
            SearchStatus.SEARCH_COMPLETE: "搜索完成",
            SearchStatus.REFINEMENT_NEEDED: "需要细化",
            SearchStatus.REFINEMENT_COMPLETE: "细化完成",
            SearchStatus.FAILED: "处理失败",
            SearchStatus.SKIPPED: "跳过处理",
        }
        
        for status, description in status_descriptions.items():
            style = STATUS_STYLES.get(status, DEFAULT_STATUS_STYLE)
            color = style.get("color", "black")
            weight = style.get("weight", 1)
            dash_array = style.get("dash_array", "")
            
            dash_style = f"border-top: {weight}px solid {color};"
            if dash_array:
                dash_style = f"border-top: {weight}px dashed {color};"
            
            legend_html += f"""
            <div style="margin: 5px 0;">
                <div style="width: 30px; height: 0; {dash_style} display: inline-block; margin-right: 10px;"></div>
                <span>{description}</span>
            </div>
            """
        
        legend_html += "</div>"
        map_obj.get_root().html.add_child(folium.Element(legend_html))

    def _add_info_panel(self, map_obj: "folium.Map", orchestration: SearchOrchestration):
        """添加统计信息面板"""
        info_text = f"""
        <b>Search Progress:</b><br>
        - Total Cells: {len(orchestration.cells)}<br>
        - Pending: {len(orchestration.get_pending_cells())}<br>
        - Current Layer: {orchestration.current_layer}<br>
        - Found Places: {len(orchestration.get_all_place_ids())}<br>
        - API Calls: {orchestration.metrics.get('total_api_calls', 0)}<br>
        - Status: {orchestration.get_overall_status()}
        """
        info_html = '''
         <div style="position: fixed;
                    top: 10px; left: 10px; width: 200px;
                    background-color:white; border:2px solid grey; z-index:9999;
                    font-size:12px; padding: 10px; font-family: monospace; border-radius: 5px;">
         {info_text}
         </div>
        '''.format(info_text=info_text.replace("\n", "<br>"))
        map_obj.get_root().html.add_child(folium.Element(info_html))

    def _record_update(self):
        """记录文件更新"""
        self.state.last_update = time.time()
        self.state.update_count += 1

    def reset_bounds(self):
        """重置固定边界，强制重新计算（调试用）"""
        logger.info("重置固定地图边界")
        self.state.fixed_bounds = None
        self.state.initial_bounds_set = False

    def create_callback(self) -> Optional[Callable]:
        """创建并返回一个封装的可视化回调处理器。"""
        if not self.config["enabled"]:
            logger.debug("可视化服务未启用，跳过回调创建")
            return None

        def visualization_callback(event_type: str, data: Dict[str, Any]):
            """可视化回调函数"""
            try:
                orchestration = data.get("orchestration")
                highlight_cell_id = data.get("cell_id")
                force_update = data.get("force", False)  # 获取force参数
                
                if orchestration:
                    logger.debug(f"可视化回调: {event_type}, cell_id={highlight_cell_id}, force={force_update}")
                    
                    # 添加重试机制
                    max_retries = 3
                    for attempt in range(max_retries):
                        try:
                            success = self.create_visualization(
                                orchestration, 
                                highlight_cell_id=highlight_cell_id, 
                                force=force_update
                            )
                            if success:
                                break
                            elif attempt < max_retries - 1:
                                logger.warning(f"可视化更新失败，重试 {attempt + 1}/{max_retries}")
                                time.sleep(0.1)  # 短暂延迟后重试
                        except Exception as e:
                            if attempt < max_retries - 1:
                                logger.warning(f"可视化更新异常，重试 {attempt + 1}/{max_retries}: {e}")
                                time.sleep(0.1)
                            else:
                                raise
                    else:
                        logger.error(f"可视化更新失败，已重试 {max_retries} 次")
                else:
                    logger.debug(f"可视化回调缺少orchestration数据: {event_type}")
            except Exception as e:
                logger.error(f"可视化回调失败: {e}", exc_info=True)

        logger.info("可视化回调已创建")
        return visualization_callback
