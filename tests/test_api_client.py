"""
重构后的API客户端测试
"""

import pytest
from unittest.mock import Mock, patch
from src.api_client import APIClient, create_api_client
from src.exceptions import SearchError, ConfigError


class MockConfig:
    """模拟配置对象，匹配重构后的Config类接口"""

    def __init__(self, dry_run=True, api_key="test_key", max_calls=1000):
        self.api_key = api_key
        self.dry_run = dry_run
        self.mock_seed = 42
        self.max_calls = max_calls
        self.api_call_count = 0

    def increment_api_calls(self):
        self.api_call_count += 1


@pytest.fixture
def mock_config():
    """创建模拟配置对象"""
    return MockConfig()


@pytest.fixture
def api_client(mock_config):
    """创建API客户端实例"""
    return APIClient(mock_config)


class TestAPIClient:
    """测试重构后的APIClient类"""

    def test_init_dry_run(self, mock_config):
        """测试干运行模式初始化"""
        mock_config.dry_run = True
        client = APIClient(mock_config)

        assert client.config == mock_config
        assert hasattr(client, "api")

    def test_init_real_api(self):
        """测试真实API模式初始化"""
        config = MockConfig(dry_run=False, api_key="real_key")
        client = APIClient(config)

        assert client.config == config
        assert hasattr(client, "api")

    def test_init_no_api_key(self):
        """测试缺少API密钥时的错误处理"""
        config = MockConfig(dry_run=False, api_key="")

        with pytest.raises(SearchError, match="API_KEY 未在配置中找到"):
            APIClient(config)

    def test_perform_nearby_search_basic(self, api_client):
        """测试基本的附近搜索功能"""
        result = api_client.perform_nearby_search(
            lat=52.52, lng=13.40, radius=1000, place_type="restaurant"
        )

        assert isinstance(result, dict)
        assert "results" in result
        assert "status" in result

    def test_perform_nearby_search_with_token(self, api_client):
        """测试带分页令牌的搜索"""
        result = api_client.perform_nearby_search(
            lat=52.52,
            lng=13.40,
            radius=1000,
            place_type="restaurant",
            next_page_token="test_token",
        )

        assert isinstance(result, dict)

    def test_get_bounding_box(self, api_client):
        """测试获取边界框功能"""
        result = api_client.get_bounding_box("Berlin, Germany")

        # 对于模拟API，可能返回None或模拟数据
        assert result is None or isinstance(result, tuple)

    def test_create_api_client_factory(self, mock_config):
        """测试工厂函数"""
        client = create_api_client(mock_config)

        assert isinstance(client, APIClient)
        assert client.config == mock_config


class TestAPIClientIntegration:
    """集成测试"""

    def test_full_search_workflow(self):
        """测试完整的搜索工作流"""
        config = MockConfig(dry_run=True, max_calls=10)
        client = APIClient(config)

        # 执行搜索
        result = client.perform_nearby_search(
            lat=52.52, lng=13.40, radius=1000, place_type="restaurant"
        )

        assert isinstance(result, dict)
        assert result["status"] == "OK"

        # 检查API调用计数
        assert config.api_call_count >= 0
