"""
可视化服务单元测试

为重构后的 VisualizationService 提供单元测试。
"""

import unittest
import tempfile
from pathlib import Path
import shutil
import time

from src.data_models import (
    GridCell,
    SearchStatus,
    create_search_orchestration_with_grid,
    SearchOrchestration
)
from src.visualization_service import VisualizationService, FOLIUM_AVAILABLE, STATUS_STYLES, HIGHLIGHT_STYLE


@unittest.skipIf(not FOLIUM_AVAILABLE, "Folium 库不可用，跳过可视化测试")
class TestVisualizationService(unittest.TestCase):
    """测试 VisualizationService"""

    def setUp(self):
        """测试前准备"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.orchestration = self._create_test_orchestration()
        self.viz_service = VisualizationService(work_dir=str(self.temp_dir), enabled=True)

    def tearDown(self):
        """测试后清理"""
        shutil.rmtree(self.temp_dir)

    def _create_test_orchestration(self) -> SearchOrchestration:
        """创建一个用于测试的编排对象"""
        grid_points = [
            (52.520, 13.405), # Pending
            (52.521, 13.406), # Processing
            (52.522, 13.407), # Search Complete
            (52.523, 13.408), # Refinement Needed
            (52.524, 13.409), # Refinement Complete
            (52.525, 13.410), # Failed
        ]
        orchestration = create_search_orchestration_with_grid(
            place_type="test_place",
            location="Test Location",
            grid_points=grid_points,
            search_radius=100.0,
        )

        # Manually set statuses for testing
        cells = list(orchestration.cells.values())
        cells[0].update_status(SearchStatus.PENDING)
        cells[1].update_status(SearchStatus.PROCESSING)
        cells[2].update_status(SearchStatus.SEARCH_COMPLETE)
        cells[3].update_status(SearchStatus.REFINEMENT_NEEDED)
        cells[4].update_status(SearchStatus.REFINEMENT_COMPLETE)
        cells[5].update_status(SearchStatus.FAILED)
        
        return orchestration

    def test_create_visualization_generates_file(self):
        """测试 create_visualization 是否能成功生成 HTML 文件"""
        output_file = self.temp_dir / "visualization_map.html"
        self.assertFalse(output_file.exists())

        success = self.viz_service.create_visualization(self.orchestration, force=True)

        self.assertTrue(success)
        self.assertTrue(output_file.exists())
        self.assertGreater(output_file.stat().st_size, 0)

    def test_visualization_content(self):
        """测试生成的 HTML 文件内容是否包含了预期的元素（新版）"""
        output_file = self.temp_dir / "visualization_map.html"
        self.viz_service.create_visualization(self.orchestration, force=True)

        content = output_file.read_text(encoding="utf-8")

        # 1. 检查是否使用了新的底图
        # Folium 在生成JS时会使用小写且无空格的名称
        self.assertIn('"cartodbpositron"', content)

        # 2. 检查是否为 Layer 0 分配了颜色
        # 我们从服务中获取预期的颜色，而不是硬编码
        expected_color = self.viz_service._get_layer_color(0)
        self.assertIn(f'"color": "{expected_color}"', content)

        # 3. 检查是否为不同状态应用了正确的线条样式
        pending_style = STATUS_STYLES[SearchStatus.PENDING]
        # Folium converts snake_case to camelCase in the final JS
        self.assertIn(f'"dashArray": "{pending_style["dash_array"]}"', content)

        refinement_style = STATUS_STYLES[SearchStatus.REFINEMENT_NEEDED]
        self.assertIn(f'"weight": {refinement_style["weight"]}', content)
        self.assertNotIn("dash_array", content.split(f'"weight": {refinement_style["weight"]}')[1].split("}")[0]) # 确保没有虚线

        # 4. 检查新的图例
        self.assertIn("<b>网格状态图例</b>", content)
        self.assertIn("<b>Layer Colors & Radii</b>", content)
        self.assertIn("Layer 0", content)
        self.assertIn("需要细化", content)

        # 5. 检查图层控制器
        self.assertIn("L.control.layers", content)

        # 6. 检查信息面板
        self.assertIn("<b>Search Progress:</b>", content)
        self.assertIn("Total Cells: 6", content)

    def test_highlight_cell(self):
        """测试高亮特定单元格的功能"""
        output_file = self.temp_dir / "visualization_map.html"
        cell_to_highlight = list(self.orchestration.cells.keys())[0]

        self.viz_service.create_visualization(
            self.orchestration, highlight_cell_id=cell_to_highlight, force=True
        )

        content = output_file.read_text(encoding="utf-8")
        self.assertIn(f'最近更新: {cell_to_highlight}', content)
        self.assertIn(HIGHLIGHT_STYLE["color"], content)

    def test_throttling(self):
        """测试可视化更新的节流功能"""
        self.viz_service.config["update_interval"] = 10 # 10 seconds
        output_file = self.temp_dir / "visualization_map.html"

        # First call should create the file
        self.assertTrue(self.viz_service.create_visualization(self.orchestration, force=False))
        self.assertTrue(output_file.exists())
        last_mod_time = output_file.stat().st_mtime

        # Second call immediately after should be throttled
        time.sleep(0.1)
        self.assertFalse(self.viz_service.create_visualization(self.orchestration, force=False))
        self.assertEqual(output_file.stat().st_mtime, last_mod_time)

        # A forced call should bypass throttling
        time.sleep(0.1)
        self.assertTrue(self.viz_service.create_visualization(self.orchestration, force=True))
        self.assertGreater(output_file.stat().st_mtime, last_mod_time)

if __name__ == "__main__":
    unittest.main()
