"""
Mock Google Places API client for testing and dry runs.
"""

import os
import random
import time
import math
from typing import List, Dict, Any, Optional


class MockDataGenerator:
    """Generates mock data for testing purposes."""

    def __init__(self, seed: Optional[int] = None):
        """Initialize the mock data generator with an optional seed."""
        if seed is not None:
            random.seed(seed)

    def generate_mock_places(
        self, count: int, query: str, location: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Generate mock place data."""
        places = []
        place_types = [
            "restaurant",
            "cafe",
            "bakery",
            "bar",
            "park",
            "museum",
            "shopping_mall",
            "gas_station",
            "lodging",
            "tourist_attraction",
            "hospital",
            "pharmacy",
        ]

        for i in range(count):
            place_type = random.choice(place_types)
            place = {
                "place_id": f"mock_place_id_{i}_{int(time.time())}",
                "name": f"Mock {query.title()} {i+1}",
                "formatted_address": f"{123+i} Main St, Mock City, MC {10000+i}",
                "location": {
                    "lat": 40.7128 + (random.random() - 0.5) * 0.1,
                    "lng": -74.0060 + (random.random() - 0.5) * 0.1,
                },
                "rating": round(random.uniform(2.5, 5.0), 1),
                "user_ratings_total": random.randint(10, 5000),
                "types": [place_type],
                "price_level": random.randint(1, 4),
                "opening_hours": {"open_now": random.choice([True, False])},
                "photos": [
                    {
                        "photo_reference": f"mock_photo_ref_{i}_1",
                        "height": 400,
                        "width": 400,
                    },
                    {
                        "photo_reference": f"mock_photo_ref_{i}_2",
                        "height": 400,
                        "width": 400,
                    },
                ],
            }
            places.append(place)

        return places

    def generate_mock_place_details(self, place_id: str) -> Dict[str, Any]:
        """Generate mock place details."""
        return {
            "place_id": place_id,
            "name": f"Details for {place_id}",
            "formatted_address": "123 Detail St, Detail City, DC 12345",
            "formatted_phone_number": "(*************",
            "international_phone_number": "****** 123 4567",
            "website": "https://example.com",
            "rating": round(random.uniform(2.5, 5.0), 1),
            "user_ratings_total": random.randint(10, 5000),
            "reviews": [
                {
                    "author_name": "John Doe",
                    "author_url": "https://example.com/johndoe",
                    "language": "en",
                    "profile_photo_url": "https://example.com/johndoe.jpg",
                    "rating": random.randint(3, 5),
                    "relative_time_description": "a week ago",
                    "text": "This is a great place!",
                    "time": int(time.time() - 7 * 24 * 60 * 60),
                },
                {
                    "author_name": "Jane Smith",
                    "author_url": "https://example.com/janesmith",
                    "language": "en",
                    "profile_photo_url": "https://example.com/janesmith.jpg",
                    "rating": random.randint(3, 5),
                    "relative_time_description": "a month ago",
                    "text": "I had a wonderful experience here.",
                    "time": int(time.time() - 30 * 24 * 60 * 60),
                },
            ],
            "opening_hours": {
                "open_now": random.choice([True, False]),
                "periods": [
                    {
                        "open": {"day": 0, "time": "0900"},
                        "close": {"day": 0, "time": "1700"},
                    },
                    {
                        "open": {"day": 1, "time": "0900"},
                        "close": {"day": 1, "time": "1700"},
                    },
                    {
                        "open": {"day": 2, "time": "0900"},
                        "close": {"day": 2, "time": "1700"},
                    },
                    {
                        "open": {"day": 3, "time": "0900"},
                        "close": {"day": 3, "time": "1700"},
                    },
                    {
                        "open": {"day": 4, "time": "0900"},
                        "close": {"day": 4, "time": "1700"},
                    },
                    {
                        "open": {"day": 5, "time": "0900"},
                        "close": {"day": 5, "time": "1700"},
                    },
                    {
                        "open": {"day": 6, "time": "0900"},
                        "close": {"day": 6, "time": "1700"},
                    },
                ],
                "weekday_text": [
                    "Monday: 9:00 AM – 5:00 PM",
                    "Tuesday: 9:00 AM – 5:00 PM",
                    "Wednesday: 9:00 AM – 5:00 PM",
                    "Thursday: 9:00 AM – 5:00 PM",
                    "Friday: 9:00 AM – 5:00 PM",
                    "Saturday: 9:00 AM – 5:00 PM",
                    "Sunday: 9:00 AM – 5:00 PM",
                ],
            },
        }


class MockPlacesAPI:
    """Mock Google Places API client for testing and dry runs."""

    def __init__(self, seed: Optional[int] = None):
        """Initialize the mock API client with an optional seed."""
        self.seed = seed or 42
        self.data_generator = MockDataGenerator(seed)
        self.refinement_config = self._load_refinement_config()
        self.refinement_counters = {}  # 跟踪每层的细化计数

    def _load_refinement_config(self) -> Dict[int, int]:
        """加载细化配置文件"""
        config = {}
        config_file = os.path.join(os.path.dirname(__file__), "../../mock_data.config")
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            if '=' in line:
                                level, count = line.split('=', 1)
                                config[int(level)] = int(count)
            except Exception as e:
                print(f"Warning: Failed to load {config_file}: {e}")
        
        # 默认配置：每层1个单元触发细化
        default_config = {0: 1, 1: 1, 2: 1, 3: 1, 4: 0}
        config.update(default_config)
        return config

    def search_places(
        self,
        query: str,
        location: Optional[str] = None,
        radius: Optional[int] = None,
        max_results: Optional[int] = None,
        region: Optional[str] = None,
        language: Optional[str] = None,
        min_rating: Optional[float] = None,
        price_level: Optional[int] = None,
        open_now: Optional[bool] = None,
        place_type: Optional[str] = None,
        fields: Optional[List[str]] = None,
    ) -> List[Dict[str, Any]]:
        """Generate mock search results for places."""
        # Default to 10 results if max_results is not specified
        count = max_results if max_results is not None else 10
        places = self.data_generator.generate_mock_places(count, query, location)

        # Apply filters
        if min_rating is not None:
            places = [p for p in places if p.get("rating", 0) >= min_rating]

        if price_level is not None:
            places = [p for p in places if p.get("price_level") == price_level]

        if open_now is not None:
            places = [
                p
                for p in places
                if p.get("opening_hours", {}).get("open_now") == open_now
            ]

        if place_type is not None:
            places = [p for p in places if place_type in p.get("types", [])]

        return places

    def get_place_details(
        self, place_id: str, fields: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Generate mock place details."""
        details = self.data_generator.generate_mock_place_details(place_id)

        # If specific fields are requested, filter the result
        if fields:
            filtered_details = {"place_id": place_id}
            for field in fields:
                if field in details:
                    filtered_details[field] = details[field]
            return filtered_details

        return details

    def get_place_photos(
        self,
        place_id: str,
        max_width: Optional[int] = None,
        max_height: Optional[int] = None,
    ) -> List[str]:
        """Generate mock photo URLs for a place."""
        # Generate 2-5 mock photo URLs
        photo_count = random.randint(2, 5)
        photo_urls = []
        for i in range(photo_count):
            photo_urls.append(f"https://example.com/mock_photo_{place_id}_{i+1}.jpg")
        return photo_urls

    def perform_nearby_search(
        self,
        lat: float,
        lng: float,
        radius: float,
        place_type: str,
        next_page_token: Optional[str] = None,
        refine_level: int = 0,
    ) -> Dict[str, Any]:
        """Perform a mock nearby search using the Google Places API."""
        # 简化的模拟逻辑，确保总是返回合理的结果
        base_seed = int(os.getenv("MOCK_SEED", self.seed))
        location_seed = (
            base_seed
            + int(lat * 10000)
            + int(lng * 10000)
            + int(radius / 100)
            + refine_level * 1000
        )
        random.seed(location_seed)

        # Parse next_page_token to determine pagination page
        if next_page_token:
            try:
                token_parts = next_page_token.split("_")
                if len(token_parts) >= 4 and token_parts[2] == "page":
                    pagination_page = int(token_parts[3])
                else:
                    pagination_page = int(token_parts[-1])
            except (ValueError, IndexError):
                pagination_page = 1
        else:
            pagination_page = 0

        # 使用动态密度区域和复杂的结果生成逻辑
        dense_areas = self._generate_dynamic_density_areas(lat, lng, radius, refine_level)
        area_density = self._get_area_density(lat, lng, dense_areas)
        
        # 使用修改后的结果计算逻辑
        should_trigger_refinement, num_results, next_token = self._calculate_results(
            area_density, pagination_page, radius, place_type, refine_level
        )

        # 生成模拟结果
        results = []
        base_hash = hash(f"{lat}_{lng}_{pagination_page}")

        for i in range(num_results):
            place_result = self._generate_simple_mock_place(
                lat, lng, base_hash, i, place_type
            )
            results.append(place_result)

        # 返回结果
        if num_results == 0:
            return {"status": "ZERO_RESULTS"}
        else:
            return {"status": "OK", "results": results, "next_page_token": next_token}

    def geocoding_search(self, location: str) -> Dict[str, Any]:
        """Perform a mock geocoding search to get coordinates for a location."""
        # Return mock coordinates for Berlin as default
        mock_results = [
            {
                "geometry": {
                    "location": {"lat": 52.5200, "lng": 13.4050},
                    "viewport": {
                        "northeast": {"lat": 52.6200, "lng": 13.5050},
                        "southwest": {"lat": 52.4200, "lng": 13.3050},
                    },
                },
                "formatted_address": f"{location}, Berlin, Germany",
            }
        ]

        return {"status": "OK", "results": mock_results}

    def get_bounding_box(self, location: str) -> Optional[tuple]:
        """Get mock bounding box for a location.

        Args:
            location: Location name/address to geocode

        Returns:
            Tuple of (min_lat, min_lng, max_lat, max_lng) or None if failed
        """
        # Return mock bounding box for Berlin as default
        return (52.4200, 13.3050, 52.6200, 13.5050)

    def _generate_dynamic_density_areas(
        self,
        center_lat: float,
        center_lng: float,
        search_radius: float,
        refine_level: int = 0,
    ) -> List[Dict[str, Any]]:
        """Generate dynamic density areas based on search center with hierarchical probability control."""
        base_seed = int(os.getenv("MOCK_SEED", self.seed))
        location_seed = (
            base_seed
            + int(center_lat * 10000)
            + int(center_lng * 10000)
            + int(search_radius / 100)
            + refine_level * 1000
        )
        random.seed(location_seed)

        dense_areas = []

        # Hierarchical probability control strategy - minimal refinement for testing:
        # L0: Very few areas contain hotspots (1-2 units total)
        # L1: Very few L0 hotspots get refined (1-2 units total)
        # L2: Very few L1 hotspots get refined (1-2 units total)
        # L3+: Very few L2 hotspots get refined (1-2 units total)

        if refine_level == 0:
            # L0: Extremely low probability for hotspots - only 1-2 units total
            area_has_hotspot_probability = 0.001  # Only 0.1% chance
            hotspot_count_range = (1, 1)  # Only 1 hotspot
            hotspot_radius_range = (800, 1500)
            medium_count_multiplier = 0.01  # Extremely few medium areas
        elif refine_level == 1:
            # L1: Extremely low probability for refinement - only 1-2 units total
            area_has_hotspot_probability = 0.0005  # Only 0.05% chance
            hotspot_count_range = (1, 1)  # Only 1 hotspot
            hotspot_radius_range = (400, 800)
            medium_count_multiplier = 0.005  # Extremely few medium areas
        elif refine_level == 2:
            # L2: Extremely low probability for refinement - only 1-2 units total
            area_has_hotspot_probability = 0.0002  # Only 0.02% chance
            hotspot_count_range = (1, 1)  # Only 1 hotspot
            hotspot_radius_range = (200, 500)
            medium_count_multiplier = 0.002  # Extremely few medium areas
        else:  # L3+
            # L3+: Extremely low probability for refinement - only 1-2 units total
            area_has_hotspot_probability = 0.0001  # Only 0.01% chance
            hotspot_radius_range = (100, 300)
            medium_count_multiplier = 0.001  # Extremely few medium areas

        # Completely disable hotspots - only use random probability for refinement
        # This ensures only 1-2 units per level trigger refinement
        return dense_areas

        # Generate high-density hotspots
        for i in range(num_hotspots):
            angle = random.uniform(0, 2 * math.pi)
            # Keep hotspots within a reasonable portion of the search radius
            max_distance = (
                search_radius * 0.4 if refine_level == 0 else search_radius * 0.3
            )
            distance = random.uniform(0, max_distance)

            hotspot_lat = center_lat + (distance / 111000) * math.cos(angle)
            hotspot_lng = center_lng + (
                distance / (111000 * math.cos(math.radians(center_lat)))
            ) * math.sin(angle)

            hotspot_radius = random.randint(*hotspot_radius_range)
            dense_areas.append(
                {
                    "center": (hotspot_lat, hotspot_lng),
                    "radius": hotspot_radius,
                    "density": "high",
                }
            )

        # Generate medium-density areas (reduced compared to high-density)
        num_medium = max(0, int(num_hotspots * medium_count_multiplier))

        for i in range(num_medium):
            angle = random.uniform(0, 2 * math.pi)
            distance = random.uniform(search_radius * 0.3, search_radius * 0.7)

            medium_lat = center_lat + (distance / 111000) * math.cos(angle)
            medium_lng = center_lng + (
                distance / (111000 * math.cos(math.radians(center_lat)))
            ) * math.sin(angle)

            # Medium areas are smaller than high-density areas
            medium_radius = random.randint(
                hotspot_radius_range[0] // 2, hotspot_radius_range[1] // 2
            )

            dense_areas.append(
                {
                    "center": (medium_lat, medium_lng),
                    "radius": medium_radius,
                    "density": "medium",
                }
            )

        return dense_areas

    def _get_area_density(
        self, lat: float, lng: float, dense_areas: List[Dict[str, Any]]
    ) -> str:
        """Get density level for a given location."""
        from ..grid_algorithms import haversine_distance

        area_density = "low"
        min_distance = float("inf")
        closest_area = None

        for area in dense_areas:
            distance = haversine_distance(
                lat, lng, area["center"][0], area["center"][1]
            )
            if distance <= area["radius"]:
                area_density = area["density"]
                closest_area = area
                break
            elif distance < min_distance:
                min_distance = distance
                closest_area = area

        # If not in any defined area, use distance-based decay
        if closest_area and area_density == "low":
            normalized_distance = min(1.0, min_distance / (closest_area["radius"] * 2))
            if normalized_distance < 0.3:
                area_density = "medium"
            elif normalized_distance < 0.6:
                area_density = "low"
            else:
                area_density = "sparse"

        return area_density

    def _calculate_results(
        self, area_density: str, pagination_page: int, radius: float, place_type: str, refine_level: int = 0
    ) -> tuple[bool, int, Optional[str]]:
        """Calculate number of results and next page token."""
        # Adjust result count based on search radius
        radius_factor = 1.0
        if radius <= 500:
            radius_factor = 0.3  # Reduced from 0.4
        elif radius <= 1000:
            radius_factor = 0.4  # Reduced from 0.6
        elif radius <= 3000:
            radius_factor = 0.6  # Reduced from 0.8

        # 根据配置文件控制细化
        max_refinements = self.refinement_config.get(refine_level, 0)
        current_refinements = self.refinement_counters.get(refine_level, 0)
        
        # 初始化默认值
        should_trigger_refinement = False
        result_count_multiplier = 0.5 * radius_factor  # 默认值
        
        # 如果当前层已经达到配置的细化数量，则不触发细化
        if current_refinements >= max_refinements:
            should_trigger_refinement = False
        else:
            # 根据密度设置概率
            if area_density == "high":
                should_trigger_refinement = random.random() < 0.15
                result_count_multiplier = 1.5 * radius_factor
            elif area_density == "medium":
                should_trigger_refinement = random.random() < 0.10
                result_count_multiplier = 1.2 * radius_factor
            elif area_density == "low":
                should_trigger_refinement = random.random() < 0.08
                result_count_multiplier = 1.0 * radius_factor
            else:  # sparse
                should_trigger_refinement = random.random() < 0.05
                result_count_multiplier = 0.8 * radius_factor
        
        # 如果触发细化，增加计数器
        if should_trigger_refinement:
            self.refinement_counters[refine_level] = current_refinements + 1

        # Calculate results based on pagination - ensure refinement continues to min_radius
        if pagination_page == 0:
            if should_trigger_refinement:
                if radius <= 500:
                    num_results = random.randint(70, 90)  # Ensure refinement triggers
                elif radius <= 1000:
                    num_results = random.randint(75, 95)  # Ensure refinement triggers
                else:
                    num_results = random.randint(80, 100)  # Ensure refinement triggers
                next_token = f"mock_token_page_1_{int(time.time())}"
            else:
                max_first_page = int(50 * result_count_multiplier)  # Reasonable for testing
                num_results = random.randint(0, max_first_page)
                pagination_threshold = 15 if radius <= 1000 else 20  # Reasonable
                next_token = (
                    None
                    if num_results < pagination_threshold
                    else f"mock_token_page_1_{int(time.time())}"
                )
        elif pagination_page == 1:
            if should_trigger_refinement:
                if radius <= 500:
                    num_results = random.randint(60, 80)  # Just enough to trigger refinement
                elif radius <= 1000:
                    num_results = random.randint(65, 85)  # Just enough to trigger refinement
                else:
                    num_results = random.randint(70, 90)  # Just enough to trigger refinement
                next_token = f"mock_token_page_2_{int(time.time())}"
            else:
                if radius <= 500:
                    max_second_page = max(
                        15, int(25 * result_count_multiplier)
                    )  # Reduced for minimal testing
                    num_results = random.randint(5, max_second_page)
                elif radius <= 1000:
                    max_second_page = max(
                        20, int(30 * result_count_multiplier)
                    )  # Reduced for minimal testing
                    num_results = random.randint(10, max_second_page)
                else:
                    max_second_page = max(
                        25, int(35 * result_count_multiplier)
                    )  # Reduced for minimal testing
                    num_results = random.randint(15, max_second_page)
                next_token = None
        elif pagination_page == 2 and should_trigger_refinement:
            # Minimal deep pagination for testing refinement
            if radius <= 500:
                min_results = max(40, int(60 * 0.6))  # Just enough to trigger refinement
                max_results = 60  # Reasonable
            elif radius <= 1000:
                min_results = max(45, int(65 * 0.7))  # Just enough to trigger refinement
                max_results = 70  # Reasonable
            else:
                min_results = max(50, int(70 * 0.8))  # Just enough to trigger refinement
                max_results = 80  # Reasonable
            num_results = random.randint(min_results, max_results)
            next_token = None
        else:
            num_results = 0
            next_token = None

        return should_trigger_refinement, num_results, next_token

    def _generate_mock_place(
        self, lat: float, lng: float, area_density: str, base_hash: int, index: int
    ) -> Dict[str, Any]:
        """Generate a mock place result."""
        place_id = f"mock_place_{area_density}_{base_hash % 10000}_{index}"
        name = f"Physio {area_density.title()} {base_hash % 1000}-{index}"

        # Generate location with variance based on density
        base_variance = 0.005 if area_density == "sparse" else 0.002
        location_variance = base_variance

        place_lat = lat + (random.random() - 0.5) * location_variance
        place_lng = lng + (random.random() - 0.5) * location_variance

        return {
            "place_id": place_id,
            "name": name,
            "geometry": {"location": {"lat": place_lat, "lng": place_lng}},
            "vicinity": f"{area_density.title()} Street, Berlin",
            "types": [
                "physiotherapist",
                "health",
                "point_of_interest",
                "establishment",
            ],
            "business_status": random.choice(
                ["OPERATIONAL", "CLOSED_TEMPORARILY", "CLOSED_PERMANENTLY"]
            ),
            "rating": round(3 + random.random() * 2, 1),
            "user_ratings_total": random.randint(1, 150),
            "plus_code": {
                "compound_code": "XXX+XX Berlin, Germany",
                "global_code": "9F4MXXX+XX",
            },
        }

    def _generate_simple_mock_place(
        self, lat: float, lng: float, base_hash: int, index: int, place_type: str
    ) -> Dict[str, Any]:
        """生成简化的模拟地点结果"""
        place_id = f"mock_place_{base_hash % 10000}_{index}"
        name = f"Mock {place_type.title()} {base_hash % 1000}-{index}"

        # 在搜索点周围生成随机位置
        location_variance = 0.003  # 约300米范围内
        place_lat = lat + (random.random() - 0.5) * location_variance
        place_lng = lng + (random.random() - 0.5) * location_variance

        return {
            "place_id": place_id,
            "name": name,
            "geometry": {"location": {"lat": place_lat, "lng": place_lng}},
            "vicinity": f"Mock Street, Test City",
            "types": [place_type, "point_of_interest", "establishment"],
            "business_status": "OPERATIONAL",
            "rating": round(3 + random.random() * 2, 1),
            "user_ratings_total": random.randint(10, 500),
            "plus_code": {
                "compound_code": "XXX+XX Test City",
                "global_code": "9F4MXXX+XX",
            },
        }
