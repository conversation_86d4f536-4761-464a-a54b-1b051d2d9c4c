"""
简化网格处理引擎模块

重构后的网格引擎，大幅简化处理逻辑，保留所有高级功能。
"""

import time
from typing import Dict, List, Tuple, Optional, Any, Callable
from dataclasses import dataclass
import math
import os
import json
import re

from src.config import Config
from src.data_models import SearchOrchestration, GridCell, SearchStatus
from src.api_client import APIClient
from src.state_manager import StateManager
from src.grid_algorithms import generate_mini_grid, haversine_distance
from src.logging_config import get_logger
from src.exceptions import SearchError

logger = get_logger(__name__)


@dataclass
class ProcessingStats:
    """处理统计信息"""

    total_cells: int = 0
    processed_cells: int = 0
    failed_cells: int = 0
    api_calls: int = 0
    places_found: int = 0
    refined_cells: int = 0


def sanitize_filename(filename: str) -> str:
    """
    将网格单元ID转换为安全的文件名
    
    Args:
        filename: 原始文件名（网格单元ID）
        
    Returns:
        安全的文件名
    """
    # 替换非法字符为下划线
    # Windows不支持的字符: \ / : * ? " < > |
    # Linux/Unix不推荐的字符: / (其他字符通常可以)
    sanitized = re.sub(r'[\\/:*?"<>|]', '_', filename)
    # 确保文件名不为空且不超过255个字符
    if not sanitized:
        sanitized = "unnamed"
    return sanitized[:255]


class GridEngine:
    """简化的网格处理引擎

    保留所有高级功能：
    - 断点续传
    - 递归细化
    - 实时可视化
    - 错误恢复
    """

    def __init__(
        self,
        orchestration: SearchOrchestration,
        api_client: APIClient,
        state_manager: StateManager,
        config: Config,
        visualization_callback: Optional[Callable] = None,
    ):
        self.orchestration = orchestration
        self.api_client = api_client
        self.state_manager = state_manager
        self.config = config
        self.visualization_callback = visualization_callback

        self.stats = ProcessingStats()
        self._update_stats()

        logger.info("网格处理引擎初始化完成")

    def _update_stats(self):
        """更新统计信息"""
        self.stats.total_cells = len(self.orchestration.cells)

        # 更准确的处理单元统计 - 包括所有已处理的状态
        self.stats.processed_cells = sum(
            1
            for cell in self.orchestration.cells.values()
            if cell.status
            in [
                SearchStatus.SEARCH_COMPLETE,
                SearchStatus.REFINEMENT_COMPLETE,
                SearchStatus.REFINEMENT_NEEDED,  # 也算作已处理
                SearchStatus.SKIPPED, # 跳过的也算作已处理
            ]
        )

        self.stats.failed_cells = sum(
            1
            for cell in self.orchestration.cells.values()
            if cell.status == SearchStatus.FAILED
        )

        # 更新地点统计
        all_place_ids = set()
        for cell in self.orchestration.cells.values():
            all_place_ids.update(cell.place_ids)
        self.stats.places_found = len(all_place_ids)

        # 更新orchestration的metrics
        self.orchestration.update_metrics(
            total_api_calls=self.stats.api_calls,
            total_places_found=self.stats.places_found,
            total_cells_processed=self.stats.processed_cells,
            failed_cells=self.stats.failed_cells
        )

    def has_pending_work(self) -> bool:
        """检查是否还有待处理的工作

        包括：
        1. 状态为 PENDING 的单元（需要搜索）
        2. 状态为 REFINEMENT_NEEDED 的单元（需要细化）
        """
        return any(
            cell.status in [SearchStatus.PENDING, SearchStatus.REFINEMENT_NEEDED]
            for cell in self.orchestration.cells.values()
        )

    def get_current_layer(self) -> int:
        """获取当前处理层级"""
        if not self.orchestration.cells:
            return 0
        
        # 找到最低的未完成层级
        all_layers = set(cell.layer_id for cell in self.orchestration.cells.values())
        logger.debug(f"所有层级: {sorted(all_layers)}")
        
        for layer in sorted(all_layers):
            # 检查这一层是否还有待处理的单元
            pending_in_layer = any(
                cell.status == SearchStatus.PENDING and cell.layer_id == layer
                for cell in self.orchestration.cells.values()
            )
            logger.debug(f"第{layer}层待处理单元: {pending_in_layer}")
            if pending_in_layer:
                logger.info(f"选择第{layer}层作为当前处理层级")
                return layer
        
        # 如果没有待处理的单元，返回最高层级
        max_layer = max(all_layers)
        logger.info(f"没有待处理单元，返回最高层级: {max_layer}")
        return max_layer

    def process_next_layer(self) -> Tuple[bool, ProcessingStats]:
        """处理下一层"""
        # 获取当前层的待处理单元
        current_layer = self.get_current_layer()
        pending_cells = [
            cell
            for cell in self.orchestration.cells.values()
            if cell.status == SearchStatus.PENDING and cell.layer_id == current_layer
        ]

        if not pending_cells:
            return self._plan_next_layer()

        # 计算当前层已处理的单元数（用于进度显示）
        completed_in_current_layer = sum(
            1 for cell in self.orchestration.cells.values()
            if cell.layer_id == current_layer and cell.status != SearchStatus.PENDING
        )

        # ===== 阶段开始日志 =====
        logger.info("=" * 60)
        logger.info(f"🚀 开始处理第 {current_layer} 层")
        logger.info(f"📊 第 {current_layer} 层有 {len(pending_cells)} 个待处理单元")
        logger.info("=" * 60)

        api_limit_reached = False
        # 处理当前层的所有单元
        for i, cell in enumerate(pending_cells):
            if self.config.api_call_count >= self.config.max_calls:
                logger.warning(f"⚠️ 达到API调用限制({self.config.max_calls})，停止处理当前层级")
                api_limit_reached = True
                break

            try:
                # 计算相对于总单元数的进度
                total_cells_in_layer = len([
                    c for c in self.orchestration.cells.values() 
                    if c.layer_id == current_layer
                ])
                current_progress = completed_in_current_layer + i + 1
                
                # ===== 单元处理进度日志 =====
                progress = f"[{current_progress}/{total_cells_in_layer}]"
                logger.info(f"🔄 {progress} 正在处理单元: {cell.cell_id}")
                logger.info(
                    f"   📍 位置: ({cell.center_lat:.6f}, {cell.center_lng:.6f})"
                )
                logger.info(f"   🎯 搜索半径: {cell.search_radius}m")

                self._process_single_cell(cell, current_progress, total_cells_in_layer)

                # 每5个单元保存一次状态
                if (i + 1) % 5 == 0:
                    self.state_manager.save_state(self.orchestration)
                    logger.info(f"💾 已处理 {current_progress} 个单元，状态已保存")

            except Exception as e:
                logger.error(f"❌ 处理单元 {cell.cell_id} 失败: {e}")
                cell.update_status(SearchStatus.FAILED)
                cell.error_message = str(e)

        # 保存最终状态
        self._update_stats()  # 先更新统计信息
        self.state_manager.save_state(self.orchestration)  # 再保存状态

        # 更新统计信息
        self._update_stats()

        # 如果API限制已达到，则停止并发出信号
        if api_limit_reached:
            if self.visualization_callback:
                try:
                    self.visualization_callback(
                        "api_limit_reached",
                        {
                            "layer": current_layer,
                            "orchestration": self.orchestration,
                            "stats": self.stats,
                        },
                    )
                except Exception as e:
                    logger.debug(f"可视化回调失败: {e}")
            return False, self.stats  # 发出信号停止主循环

        # 触发可视化回调
        if self.visualization_callback:
            try:
                self.visualization_callback(
                    "layer_complete",
                    {
                        "layer": current_layer,
                        "orchestration": self.orchestration,  # 传递完整的编排对象
                        "stats": self.stats,
                    },
                )
            except Exception as e:
                logger.debug(f"可视化回调失败: {e}")

        # ===== 阶段完成日志 =====
        logger.info("=" * 60)
        logger.info(f"✅ 第 {current_layer} 层处理完成")
        logger.info(
            f"📈 统计: 处理={self.stats.processed_cells}, 失败={self.stats.failed_cells}, 地点={self.stats.places_found}"
        )
        logger.info("=" * 60)

        # 检查是否还有更多工作
        return self.has_pending_work(), self.stats

    def _get_grandparent_cell(self, cell: GridCell) -> Optional[GridCell]:
        """获取祖父网格单元（父网格的父网格）
        
        Args:
            cell: 当前网格单元
            
        Returns:
            Optional[GridCell]: 祖父网格单元，如果没有则返回None
        """
        if not cell.parent_id:
            return None
        
        parent_cell = self.orchestration.cells.get(cell.parent_id)
        if not parent_cell or not parent_cell.parent_id:
            return None
        
        grandparent_cell = self.orchestration.cells.get(parent_cell.parent_id)
        return grandparent_cell

    def _do_cells_intersect(self, cell1: GridCell, cell2: GridCell) -> bool:
        """判断两个圆形网格单元是否相交
        
        通过计算两个圆心之间的距离并与它们的半径之和进行比较来确定相交。
        
        Args:
            cell1: 第一个网格单元
            cell2: 第二个网格单元
            
        Returns:
            bool: 如果两个圆形区域相交则返回True，否则返回False
        """
        # 计算两个圆心之间的距离
        distance = haversine_distance(
            cell1.center_lat, cell1.center_lng,
            cell2.center_lat, cell2.center_lng
        )
        
        # 计算半径之和
        sum_of_radii = cell1.search_radius + cell2.search_radius
        
        # 如果距离小于或等于半径之和，则它们相交
        return distance <= sum_of_radii

    def _should_process_cell(self, cell: GridCell) -> bool:
        """判断是否应该处理当前网格单元
        
        检查当前网格是否与祖父网格相交，如果不相交则跳过
        
        Args:
            cell: 要检查的网格单元
            
        Returns:
            bool: 是否应该处理
        """
        # 如果是根网格（没有父网格），直接处理
        if not cell.parent_id:
            return True
        
        # 获取祖父网格
        grandparent_cell = self._get_grandparent_cell(cell)
        if not grandparent_cell:
            # 如果没有祖父网格，说明是第二层，直接处理
            return True
        
        # 检查当前网格是否与祖父网格相交
        intersects = self._do_cells_intersect(cell, grandparent_cell)
        
        if not intersects:
            logger.debug(
                f"⏭️ 跳过单元 {cell.cell_id}: 不与祖父网格 {grandparent_cell.cell_id} 相交"
            )
        
        return intersects

    def _process_single_cell(
        self,
        cell: GridCell,
        current_index: int = 0,
        total_count: int = 0,
        stats: Optional[ProcessingStats] = None,
    ):
        """处理单个网格单元

        Args:
            cell: 要处理的网格单元
            current_index: 当前处理的索引（用于进度显示）
            total_count: 总单元数（用于进度显示）
            stats: 统计信息对象
        """
        if stats is None:
            stats = self.stats

        # 检查是否应该处理这个网格单元
        if not self._should_process_cell(cell):
            # 标记为跳过
            cell.update_status(SearchStatus.SKIPPED)
            cell.metadata["skipped_reason"] = "outside_grandparent_bounds"
            cell.metadata["grandparent_cell_id"] = self._get_grandparent_cell(cell).cell_id if self._get_grandparent_cell(cell) else None
            logger.info(f"⏭️ 跳过单元 {cell.cell_id}: 超出祖父网格范围")
            
            # 更新统计信息
            stats.processed_cells += 1
            self._update_stats()
            
            # 保存状态
            self.state_manager.save_state(self.orchestration)
            
            # 触发可视化更新
            if self.visualization_callback:
                try:
                    logger.debug(f"触发可视化更新: cell_id={cell.cell_id}")
                    self.visualization_callback(
                        "cell_processed",
                        {
                            "cell_id": cell.cell_id,
                            "orchestration": self.orchestration,
                            "force": True,  # 强制更新
                        },
                    )
                except Exception as e:
                    logger.error(f"可视化回调失败: {e}", exc_info=True)
            
            return

        # 更新状态为处理中
        cell.update_status(SearchStatus.PROCESSING)

        try:
            # 执行搜索
            results, api_calls_made = self._perform_search(cell)

            # 处理搜索结果
            self._handle_search_results(cell, results, api_calls_made, stats)

            # 详细的处理结果日志
            status_emoji = (
                "🔄" if cell.status == SearchStatus.REFINEMENT_NEEDED else "✅"
            )
            logger.info(
                f"   {status_emoji} 完成: 找到 {len(results)} 个结果, API调用 {api_calls_made} 次"
            )
            if cell.status == SearchStatus.REFINEMENT_NEEDED:
                logger.info(
                    f"   🔍 标记为需要细化 (总结果数 >= {self.config.subdivision_threshold})"
                )

        except SearchError as e:
            logger.error(f"❌ 单元 {cell.cell_id} 处理失败: {e}")
            cell.update_status(SearchStatus.FAILED)
            cell.error_message = str(e)
            stats.failed_cells += 1
            
            # 检查是否是REQUEST_DENIED错误，如果是则立即停止程序
            if "REQUEST_DENIED" in str(e):
                logger.error("🚨 检测到 REQUEST_DENIED 错误，这通常意味着API密钥无效或配额用完")
                logger.error("🚨 程序将立即停止，请检查API密钥和配额设置")
                raise SystemExit(1)
                
        except Exception as e:
            logger.error(f"❌ 单元 {cell.cell_id} 处理失败: {e}")
            cell.update_status(SearchStatus.FAILED)
            cell.error_message = str(e)
            stats.failed_cells += 1
        finally:
            # 无论成功与否，都触发一次可视化更新
            if self.visualization_callback:
                try:
                    logger.debug(f"触发可视化更新: cell_id={cell.cell_id}")
                    self.visualization_callback(
                        "cell_processed",
                        {
                            "cell_id": cell.cell_id,
                            "orchestration": self.orchestration,
                            "force": True,  # 强制更新
                        },
                    )
                except Exception as e:
                    logger.error(f"可视化回调失败: {e}", exc_info=True)

    def _perform_search(self, cell: GridCell) -> tuple[List[Dict], int]:
        """执行搜索API调用

        Returns:
            tuple: (搜索结果列表, API调用次数)
        """
        all_results = []
        next_page_token = None
        api_calls_made = 0

        while api_calls_made < self.config.max_api_calls_per_cell:
            # 执行API调用
            response = self.api_client.perform_nearby_search(
                lat=cell.center_lat,
                lng=cell.center_lng,
                radius=cell.search_radius,
                place_type=self.orchestration.place_type,
                next_page_token=next_page_token,
                refine_level=cell.layer_id,
            )

            # 保存API响应数据
            try:
                # 为每次调用生成唯一的文件名
                safe_cell_id = sanitize_filename(cell.cell_id)
                response_filename = f"{safe_cell_id}_page_{api_calls_made}.json"
                response_file_path = os.path.join(self.config.response_details_dir, response_filename)
                
                # 保存响应数据
                with open(response_file_path, "w", encoding="utf-8") as f:
                    json.dump(response, f, indent=2, ensure_ascii=False)
                logger.debug(f"已保存API响应数据到: {response_file_path}")
            except Exception as e:
                logger.warning(f"保存API响应数据失败: {e}")

            api_calls_made += 1
            self.stats.api_calls += 1

            # 增加主配置的API调用计数
            self.config.increment_api_calls()

            # 处理响应
            if response.get("status") == "OK":
                results = response.get("results", [])
                all_results.extend(results)
                next_page_token = response.get("next_page_token")

                if not next_page_token:
                    break

                # 添加页面延迟（仅对真实API）
                if not getattr(self.api_client.config, "dry_run", False):
                    time.sleep(self.config.page_delay_seconds)

            elif response.get("status") == "ZERO_RESULTS":
                break
            else:
                # API错误
                raise SearchError(f"API调用失败: {response.get('status')}")

        return all_results, api_calls_made

    def _handle_search_results(
        self,
        cell: GridCell,
        results: List[Dict],
        api_calls_made: int,
        stats: ProcessingStats,
    ):
        """处理搜索结果

        Args:
            cell: 网格单元
            results: 搜索结果列表
            api_calls_made: 实际进行的API调用次数
            stats: 统计信息对象
        """
        # 处理结果
        place_ids = {
            result.get("place_id") for result in results if result.get("place_id")
        }
        place_ids.discard(None)

        # 记录搜索结果 - 使用实际的API调用次数
        cell.record_search_results(len(results), place_ids, api_calls_made)

        # 更新统计信息
        stats.places_found += len(place_ids)

        # 同步到主配置对象
        self.config.add_found_places(place_ids)

        # 使用改进的细化判断逻辑
        if self._should_refine_cell(cell, results):
            # 标记为需要细化
            cell.update_status(SearchStatus.REFINEMENT_NEEDED)
            stats.refined_cells += 1
            logger.debug(f"单元 {cell.cell_id} 需要细化 (结果数: {len(results)})")
        else:
            # 搜索完成
            cell.update_status(SearchStatus.SEARCH_COMPLETE)
            logger.debug(f"单元 {cell.cell_id} 搜索完成 (结果数: {len(results)})")

    def _plan_next_layer(self) -> Tuple[bool, ProcessingStats]:
        """规划下一层的细化"""
        cells_to_refine = [
            cell for cell in self.orchestration.cells.values()
            if cell.status == SearchStatus.REFINEMENT_NEEDED
        ]
        
        if not cells_to_refine:
            logger.info("🎉 没有需要细化的单元，搜索完成")
            return False, self.stats
        
        next_layer = cells_to_refine[0].layer_id + 1
        logger.info(f"🔍 开始生成第 {next_layer} 层细化网格")
        logger.info(f"📊 需要细化 {len(cells_to_refine)} 个单元")
        
        # 收集所有子网格点
        all_mini_points = {}  # 使用dict存储点 -> 父网格列表
        all_mini_points_before_deduplication = 0
        new_cells_count = 0
        
        # 第一步：收集所有子网格点及其父网格
        for i, cell in enumerate(cells_to_refine):
            try:
                new_radius = cell.search_radius / self.config.mini_radius_factor
                step_meters = new_radius * self.config.mini_grid_overlap_factor
                
                mini_grid_points = generate_mini_grid(
                    center_point=(cell.center_lat, cell.center_lng),
                    area_radius=cell.search_radius,
                    step_meters=step_meters,
                )
                
                all_mini_points_before_deduplication += len(mini_grid_points)
                
                # 记录每个点属于哪些父网格
                for lat, lng in mini_grid_points:
                    point_key = (round(lat, 6), round(lng, 6))
                    if point_key not in all_mini_points:
                        all_mini_points[point_key] = []
                    all_mini_points[point_key].append(cell)
                
                logger.info(f"  - [{i + 1}/{len(cells_to_refine)}] 单元 {cell.cell_id} 生成了 {len(mini_grid_points)} 个点")
                
            except Exception as e:
                logger.error(f"❌ 为单元 {cell.cell_id} 生成子网格失败: {e}")
                cell.update_status(SearchStatus.FAILED)
                cell.error_message = str(e)
        
        # 第二步：创建去重后的子网格单元
        logger.info(f"🌍 去重前共获得 {all_mini_points_before_deduplication} 个子网格点")
        logger.info(f"🌍 去重后共获得 {len(all_mini_points)} 个唯一的子网格点")
        
        for point_key, parent_cells in all_mini_points.items():
            lat, lng = point_key
            
            # 选择最近的父网格作为主父网格
            main_parent = min(parent_cells, key=lambda p: 
                haversine_distance(p.center_lat, p.center_lng, lat, lng)
            )
            
            new_radius = main_parent.search_radius / self.config.mini_radius_factor
            
            new_cell = GridCell(
                cell_id="",
                status=SearchStatus.PENDING,
                layer_id=next_layer,
                center_lat=lat,
                center_lng=lng,
                search_radius=new_radius,
                parent_id=main_parent.cell_id,
            )
            
            # 确保不重复添加
            if new_cell.cell_id not in self.orchestration.cells:
                self.orchestration.cells[new_cell.cell_id] = new_cell
                new_cells_count += 1
        
        # 第三步：更新父网格状态
        for cell in cells_to_refine:
            if cell.status == SearchStatus.REFINEMENT_NEEDED:
                cell.update_status(SearchStatus.REFINEMENT_COMPLETE)
        
        logger.info(f"✅ 成功创建了 {new_cells_count} 个新的子单元")
        
        # 保存状态和触发回调
        self.state_manager.save_state(self.orchestration)
        self._update_stats()
        
        return True, self.stats

    def _should_refine_cell(self, cell: GridCell, results: List[Dict]) -> bool:
        """改进的细化判断逻辑
        
        先获取所有分页结果，然后根据总结果数判断是否需要细化。
        
        Args:
            cell: 要判断的网格单元
            results: 所有分页的搜索结果列表
            
        Returns:
            bool: 是否需要细化
        """
        total_results = len(results)
        
        logger.debug(
            f"检查单元 {cell.cell_id} 是否需要细化: 总结果数={total_results}, 层级={cell.layer_id}, 半径={cell.search_radius}"
        )

        # 1. 检查是否已达到最大细化层级或最小半径
        if (
            cell.layer_id >= self.config.max_refinement_levels
            or cell.search_radius <= self.config.min_refinement_radius
        ):
            logger.debug(
                f"单元 {cell.cell_id} 不满足基本条件: 层级={cell.layer_id}/{self.config.max_refinement_levels}, 半径={cell.search_radius}/{self.config.min_refinement_radius}"
            )
            return False

        # 2. 检查总结果数量是否达到细分阈值
        if total_results >= self.config.subdivision_threshold:
            logger.debug(
                f"单元 {cell.cell_id} 总结果数量满足细分条件: {total_results} >= {self.config.subdivision_threshold}"
            )
            return True

        # 默认不细分
        logger.debug(
            f"单元 {cell.cell_id} 总结果数量不足，不进行细分: {total_results} < {self.config.subdivision_threshold}"
        )
        return False

    def _verify_coverage(self, cells_to_refine: List[GridCell], new_cells: List[GridCell]) -> bool:
        """验证细化后的覆盖是否完整"""
        # 检查每个父网格的搜索区域是否被新网格覆盖
        for parent in cells_to_refine:
            parent_covered = False
            for child in new_cells:
                distance = haversine_distance(
                    parent.center_lat, parent.center_lng,
                    child.center_lat, child.center_lng
                )
                # 检查子网格是否覆盖父网格的中心区域
                if distance <= parent.search_radius - child.search_radius:
                    parent_covered = True
                    break
            
            if not parent_covered:
                logger.warning(f"⚠️ 父网格 {parent.cell_id} 的覆盖可能不完整")
                return False
        
        return True
