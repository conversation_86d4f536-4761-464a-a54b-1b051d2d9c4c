#!/usr/bin/env python3
"""
重构后的主程序

大幅简化的主程序流程，保留所有高级功能但减少复杂性。
"""

import time
import os
from typing import Optional

from src.config import create_config, create_parser, Config
from src.api_client import create_api_client, APIClient
from src.grid_algorithms import generate_grid_points, calculate_search_parameters
from src.data_models import create_search_orchestration_with_grid, SearchOrchestration
from src.state_manager import StateManager
from src.grid_engine import GridEngine
from src.reporter import log_final_summary, save_found_places
from src.logging_config import configure_logging, get_logger
from src.exceptions import SearchError, ConfigError
from src.visualization_service import VisualizationService

logger = get_logger(__name__)


def setup_search_area(config: Config, api_client: APIClient) -> tuple:
    """设置搜索区域并返回边界"""
    bounds = config.get_search_bounds()
    if bounds:
        logger.info(f"使用配置的搜索区域: {config.get_location_name()}")
        logger.info(f"搜索区域边界: {bounds}")
    else:
        # 只有在没有指定test_area时才调用API获取边界
        if config.test_area:
            logger.info(f"指定了测试区域 '{config.test_area}'，跳过API边界查询")
            # 使用默认边界或抛出错误
            raise SearchError(f"测试区域 '{config.test_area}' 未找到预设边界配置")
        else:
            bounds = api_client.get_bounding_box(config.location)
            if bounds is None:
                raise SearchError(f"无法获取位置 '{config.location}' 的边界框")
            logger.info(f"使用API获取的边界框: {bounds}")
    config.search_bounds = bounds
    return bounds


def create_or_load_orchestration(
    state_manager: StateManager, config: Config, bounds: tuple
) -> SearchOrchestration:
    """创建或加载搜索编排对象"""
    try:
        orchestration = state_manager.load_state_with_fallback()
        if orchestration is not None:
            logger.info("从现有状态加载搜索编排对象")
            
            # 恢复Config对象的运行时状态
            config.api_call_count = orchestration.metrics.get("total_api_calls", 0)
            config.found_places = orchestration.get_all_place_ids()
            
            logger.info(f"已恢复状态: API调用={config.api_call_count}, 地点数={len(config.found_places)}")
            return orchestration
    except Exception as e:
        logger.warning(f"加载状态失败: {e}，将创建新的编排对象")

    logger.info("创建新的搜索编排对象")
    if config.initial_radius is not None and config.initial_grid_step is not None:
        initial_radius, initial_grid_step = config.initial_radius, config.initial_grid_step
        logger.info(f"使用用户指定参数: 半径={initial_radius}m, 步长={initial_grid_step}m")
    else:
        initial_radius, initial_grid_step, _ = calculate_search_parameters(
            bounds, config.max_radius, config.min_refinement_radius, config.max_refinement_levels,
            config.initial_radius_factor, config.grid_step_factor
        )
        logger.info(f"使用自动计算参数: 半径={initial_radius}m, 步长={initial_grid_step}m")

    logger.info(f"生成L0初始网格，步长: {initial_grid_step:.2f}m...")
    grid_points = generate_grid_points(bounds, initial_grid_step)
    logger.info(f"生成了 {len(grid_points)} 个L0网格点")

    orchestration_config = {
        "max_refinement_levels": config.max_refinement_levels,
        "initial_radius": initial_radius,
        "min_refinement_radius": config.min_refinement_radius,
        "grid_overlap_factor": config.grid_step_factor,
    }

    orchestration = create_search_orchestration_with_grid(
        place_type=config.place_type, location=config.location, grid_points=grid_points,
        search_radius=initial_radius, config=orchestration_config
    )
    state_manager.save_state(orchestration)
    logger.info("初始网格已生成并保存")
    return orchestration


def create_grid_engine(
    orchestration: SearchOrchestration, api_client: APIClient, state_manager: StateManager,
    config: Config, viz_service: Optional[VisualizationService]
) -> GridEngine:
    """创建网格处理引擎"""
    return GridEngine(
        orchestration=orchestration, api_client=api_client, state_manager=state_manager,
        config=config, visualization_callback=viz_service.create_callback() if viz_service else None
    )

def run_search_loop(grid_engine: GridEngine, config: Config) -> float:
    """执行主搜索循环"""
    start_time = time.time()
    logger.info("🚀" * 20)
    logger.info("🚀 开始主搜索循环")
    logger.info("🚀" * 20)

    loop_iteration = 0
    while grid_engine.has_pending_work():
        try:
            loop_iteration += 1
            logger.info(f"\n🔄 搜索循环第 {loop_iteration} 轮")
            has_more, stats = grid_engine.process_next_layer()

            elapsed = time.time() - start_time
            logger.info("\n📊 当前搜索统计:")
            logger.info(f"   ⏱️  运行时间: {elapsed:.1f}s")
            logger.info(f"   🔗 API调用: {config.api_call_count}")
            logger.info(f"   📍 找到地点: {len(config.found_places)}")
            logger.info(f"   📦 处理单元: {stats.processed_cells}/{stats.total_cells}")
            logger.info(f"   ❌ 失败单元: {stats.failed_cells}")

            if not has_more:
                break
        except Exception as e:
            logger.error(f"❌ 搜索循环出错: {e}", exc_info=True)
            raise SearchError(f"搜索失败: {e}") from e

    total_time = time.time() - start_time
    logger.info("🎉" * 20)
    logger.info(f"🎉 主搜索循环完成，共进行了 {loop_iteration} 轮")
    logger.info("🎉" * 20)
    return total_time

def generate_reports(
    orchestration: SearchOrchestration, config: Config, total_time: float,
    viz_service: Optional[VisualizationService]
):
    """生成可视化和报告"""
    logger.info("📊" * 20)
    logger.info("📊 开始生成可视化和报告")
    logger.info("📊" * 20)

    try:
        if viz_service:
            logger.info("🎨 生成最终可视化...")
            success = viz_service.create_visualization(orchestration, force=True)
            if success:
                viz_file = os.path.join(config.output_dir, "visualization_map.html")
                logger.info(f"✅ 最终可视化已保存到 {viz_file}")
            else:
                logger.warning("⚠️ 最终可视化生成失败")

        log_final_summary(orchestration, config, total_time)
        save_found_places(orchestration, config)
        logger.info("✅ 报告和结果文件生成完成")

    except Exception as e:
        logger.error(f"❌ 生成报告失败: {e}", exc_info=True)
        raise SearchError(f"报告生成失败: {e}") from e


def main():
    """简化的主程序入口点"""
    try:
        parser = create_parser()
        args = parser.parse_args()
        configure_logging(log_level=args.log_level)

        logger.info("=== Google Maps Grid Search 启动 ===")
        logger.info(f"工作目录: {args.work_dir}")

        config = create_config(args)
        config.log_summary()

        api_client = create_api_client(config)
        bounds = setup_search_area(config, api_client)

        state_manager = StateManager(
            work_dir=config.output_dir, state_file="orchestration.json"
        )

        orchestration = create_or_load_orchestration(state_manager, config, bounds)

        viz_service = VisualizationService(config.output_dir, enabled=config.visualize)

        grid_engine = create_grid_engine(
            orchestration, api_client, state_manager, config, viz_service
        )

        total_time = run_search_loop(grid_engine, config)

        generate_reports(orchestration, config, total_time, viz_service)

        logger.info("=== 搜索完成! ===")
        config.log_summary()

    except KeyboardInterrupt:
        logger.info("用户中断操作")
    except (SearchError, ConfigError) as e:
        logger.error(f"搜索失败: {e}")
        raise
    except Exception as e:
        logger.error(f"未知错误: {e}", exc_info=True)
        raise SearchError(f"程序执行失败: {e}") from e


if __name__ == "__main__":
    main()