"""
报告生成模块

负责生成和输出最终的统计报告和结果文件。
"""

import json
import os
import logging
from typing import Dict, Any

from src.data_models import SearchOrchestration, SearchStatus
from src.config import Config

logger = logging.getLogger(__name__)


def log_final_summary(
    orchestration: SearchOrchestration, config: Config, total_time: float
):
    """
    格式化并记录最终的统计摘要。

    Args:
        orchestration: 搜索编排对象。
        config: 统一配置对象。
        total_time: 总运行时间。
    """
    all_place_ids = orchestration.get_all_place_ids()

    completed_cells = [
        c
        for c in orchestration.cells.values()
        if c.status in [SearchStatus.SEARCH_COMPLETE, SearchStatus.REFINEMENT_COMPLETE]
    ]
    failed_cells = [
        c for c in orchestration.cells.values() if c.status == SearchStatus.FAILED
    ]
    refinement_needed_cells = [
        c
        for c in orchestration.cells.values()
        if c.status == SearchStatus.REFINEMENT_NEEDED
    ]

    logger.info("🎯" * 30)
    logger.info("🎯 搜索完成 - 最终统计报告")
    logger.info("🎯" * 30)
    logger.info(f"⏱️  总运行时间: {total_time:.1f} 秒")
    logger.info(f"📦 网格单元统计:")
    logger.info(f"   ✅ 已完成: {len(completed_cells)}")
    logger.info(f"   🔄 需要细化 (未完成): {len(refinement_needed_cells)}")
    logger.info(f"   ❌ 失败: {len(failed_cells)}")
    logger.info(f"   📊 总计: {len(orchestration.cells)}")
    logger.info(f"🔗 API调用次数: {config.api_call_count}")
    logger.info(f"📍 找到的唯一地点数: {len(all_place_ids)}")
    logger.info("🎯" * 30)


def save_found_places(orchestration: SearchOrchestration, config: Config):
    """
    将所有发现的地点ID保存到JSON文件。

    Args:
        orchestration: 搜索编排对象。
        config: 统一配置对象。
    """
    all_place_ids = list(orchestration.get_all_place_ids())
    place_ids_file = os.path.join(config.output_dir, "found_place_ids.json")

    try:
        with open(place_ids_file, "w", encoding="utf-8") as f:
            json.dump(all_place_ids, f, indent=2, ensure_ascii=False)
        logger.info(f"💾 结果已保存到: {place_ids_file}")
    except IOError as e:
        logger.error(f"❌ 保存地点ID文件失败: {e}")
